<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="1c24e1f2-ced0-42ea-8c34-6fb3e336fa64" name="Changes" comment="update: 生成部分了anytxn-parameter的单元测试类">
      <change afterPath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/account/service/ParmAcctPmtAllocPersonalServiceTest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/account/service/SinagHolidayServiceTest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/card/service/CardBinDefinitionServiceTest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/card/service/CardBinServiceTest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/card/service/CardCustomServiceTest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/card/service/CardFaceLayoutInfoServiceTest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/card/service/CardFaceServiceTest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/card/service/CardIssueServiceTest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/card/service/CardProductCurrencyRelationServiceTest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/card/service/ParmAnnualFeeRuleServiceTest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/card/service/ParmCardFeeDefiniTionServiceTest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/common/service/CurrencyCodeServiceTest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/installment/service/InstallEarlyTerminationParmServiceTest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/installment/service/InstallFeeCodeInfoServiceTest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/installment/service/InstallProductInfoServiceTest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/installment/service/InstallTypeParmServiceTest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/installment/service/InstallmentInterestInfoServiceTest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/service/ParmCardFeeGroupServiceTest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/service/utils/SimpleMockUtils.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/service/utils/SimpleTestUtils.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/anytxn-third-party-service/anytxn-file-manager/anytxn-file-manager-server/src/main/resources/application.yml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/anytxn-third-party-service/anytxn-notification/anytxn-notification-server/src/main/resources/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-business-core/anytxn-business-core-sdk/src/test/java/com/anytech/anytxn/business/config/DBConfig.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-business-core/anytxn-business-core-sdk/src/test/java/com/anytech/anytxn/business/demo/DemoBO.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-business-core/anytxn-business-core-sdk/src/test/java/com/anytech/anytxn/business/demo/DemoDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-business-core/anytxn-business-core-sdk/src/test/java/com/anytech/anytxn/business/demo/DemoMainTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-business-core/anytxn-business-core-sdk/src/test/java/com/anytech/anytxn/business/demo/IDemo.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-business-core/anytxn-business-core-sdk/src/test/java/com/anytech/anytxn/business/jdbc/diffTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-business-core/anytxn-business-core单元测试修改记录.md" beforeDir="false" afterPath="$PROJECT_DIR$/anytxn-business-core/anytxn-business-core单元测试修改记录.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/account/service/ParmChequeBackServiceTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/account/service/ParmChequeBackServiceTest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/common/service/account/AcctLimitCtrlServiceTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/common/service/account/AutoPaymentTableServiceTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/common/service/account/BalanceInwardTransferServiceTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/common/service/account/BlockCodeAccountServiceTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/common/service/account/CashFeeTableServiceTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/common/service/account/DelayedCloseDaysServiceTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/common/service/account/DelinquentControlDefineServiceTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/common/service/account/DelinquentControlServiceTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/common/service/account/InterestBearingServiceTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/common/service/account/InterestHisServiceTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/common/service/account/InterestServiceTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/common/service/account/InterestSettlementServiceTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/common/service/account/LargeGraceInfoServiceTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/common/service/account/LateFeeTableServiceTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/common/service/account/MinimumPaymentPercentServiceTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/common/service/account/ParmAcctPmtAllocBasicDefServiceTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/common/service/account/ParmBalancePricingTableServiceTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/common/service/account/ParmChequeBackServiceTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/common/service/account/ParmGiroAutoPaymentServiceTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/common/service/account/PaymentAllocatedControlServiceTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/common/service/account/PaymentAllocatedServiceTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/common/service/account/QueryTransFeeServiceTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/common/service/authorization/AccountGroupAuthControlServiceTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/common/service/authorization/AuthCheckControlServiceTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/common/service/authorization/AuthCheckDefinitionServiceTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/common/service/authorization/AuthorisationProcessingServiceTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/common/service/authorization/AuthorizationRuleServiceTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/common/service/authorization/CmBizCommitAuditServiceTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/common/service/audit/CmBizCommitAuditServiceTest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/common/service/authorization/CountryMccCheckServiceTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/common/service/authorization/CountryMccServiceSimpleTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/common/service/authorization/CountryMccServiceTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/common/service/audit/CountryMccServiceTest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/common/service/authorization/CreditTransactionOverpayLimitServiceTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/common/service/authorization/MccCodeServiceTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/common/service/authorization/MccGroupDefinitionServiceTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/common/service/authorization/MccGroupDetailServiceTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/common/service/authorization/MerchantFraudServiceTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/common/service/authorization/ParmTransFeeServiceTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/common/service/authorization/ParmTypeDetailCodeServiceTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/common/service/authorization/SafeLockServiceTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/common/service/authorization/VelocityControlServiceTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/common/service/authorization/VelocityDefinitionServiceTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/common/service/authorization/VelocitySetDefinitionServiceTest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter单元测试修改记录.md" beforeDir="false" afterPath="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter单元测试修改记录.md" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ChangesViewManager">
    <option name="groupingKeys">
      <option value="directory" />
      <option value="module" />
      <option value="repository" />
    </option>
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$/anytxn-third-party-service" value="feature_project-ai-20250610" />
      </map>
    </option>
    <option name="RECENT_COMMON_BRANCH" value="feature_project-ai-readme-20250610" />
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/anytxn-parameter" />
    <option name="ROOT_SYNC" value="SYNC" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\maven\apache-maven-3.6.3" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\maven\apache-maven-3.6.3\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="2yLHB3NXY1mtefbU6oyH3PIBKeg" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="openDirectoriesWithSingleClick" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "JUnit.AQRCTest.executor": "Coverage",
    "JUnit.AccountAbsStautsServiceTest.executor": "Coverage",
    "JUnit.AccountCommonServiceTest.executor": "Coverage",
    "JUnit.AccountProductInfoMapperTest.executor": "Coverage",
    "JUnit.AccountantGlactbalSelfMapperTest.executor": "Run",
    "JUnit.AccountantGlvatbalSelfMapperTest.executor": "Coverage",
    "JUnit.AcctLimitCtrlServiceTest.executor": "Coverage",
    "JUnit.AppPushSendMessageTest.executor": "Coverage",
    "JUnit.AuthCheckControlServiceTest.executor": "Run",
    "JUnit.AuthCheckDefinitionServiceTest.executor": "Run",
    "JUnit.AuthorizationRuleServiceTest.executor": "Run",
    "JUnit.AutoPaymentTableServiceTest.executor": "Run",
    "JUnit.CardAcctCustReleationServiceTest.executor": "Coverage",
    "JUnit.CardBinDefinitionServiceImplTest.executor": "Coverage",
    "JUnit.CardBinDefinitionServiceTest.executor": "Run",
    "JUnit.CardBinServiceTest.executor": "Coverage",
    "JUnit.CardCustSpecialInfoServiceTest.executor": "Coverage",
    "JUnit.CardCustomServiceTest.executor": "Coverage",
    "JUnit.CardFaceServiceTest.executor": "Debug",
    "JUnit.CardManageFeeRecordServiceTest.executor": "Coverage",
    "JUnit.CardMcAbuLogServiceTest.executor": "Run",
    "JUnit.CardMdesNotificationServiceTest.executor": "Coverage",
    "JUnit.CashFeeTableServiceTest.executor": "Run",
    "JUnit.CommonAccountServiceTest.executor": "Coverage",
    "JUnit.CorporateTopDownReferenceServiceTest.executor": "Coverage",
    "JUnit.CreateSecretKeyControllerTest.executor": "Coverage",
    "JUnit.CreditTransactionOverpayLimitServiceTest.executor": "Run",
    "JUnit.CusAccountCommonServiceTest.executor": "Coverage",
    "JUnit.CustAccountWriterServiceTest.executor": "Coverage",
    "JUnit.CustReconciliationControlServiceTest.executor": "Run",
    "JUnit.DelayedCloseDaysServiceTest.executor": "Run",
    "JUnit.DelinquentControlServiceTest.executor": "Run",
    "JUnit.EmailSendMessageTest.executor": "Coverage",
    "JUnit.EncryptionApiTest.executor": "Coverage",
    "JUnit.EncryptionApiTest.shouldThrowNoClassDefFoundError_whenEncryptionByDeCalled_dueToConfigIssue.executor": "Run",
    "JUnit.EncryptionManagerTest.executor": "Coverage",
    "JUnit.EncryptionManagerTest.shouldEncryptionPinByBa_whenValidPasswordDTO_thenReturnEncryptedPin.executor": "Run",
    "JUnit.ExceptionRecordServiceTest.executor": "Coverage",
    "JUnit.FileCopyServiceTest.executor": "Coverage",
    "JUnit.FileCopyServiceTest.shouldCopyFileToSubdirectory_whenCopyPathHasSubdirectory_thenCreateDirectoryAndCopy.executor": "Run",
    "JUnit.FileManagerScanProcessServiceTest.executor": "Coverage",
    "JUnit.FileManagerScanProcessServiceTest.shouldUpdateParamStatus_whenValidMessageAndParam_thenUpdateSuccessfully.executor": "Run",
    "JUnit.FileScanMainOperatorTest.executor": "Coverage",
    "JUnit.FinancialTranSendMessageTest.executor": "Coverage",
    "JUnit.IEncryptionControllerTest.executor": "Coverage",
    "JUnit.InstallmentLimitUnitCrossServiceTest.executor": "Coverage",
    "JUnit.InterestServiceTest.executor": "Run",
    "JUnit.JdbcServiceProxyTest.executor": "Coverage",
    "JUnit.LimitOverpayComponentServiceTest.executor": "Run",
    "JUnit.MaintenanceLogBisServiceTest.executor": "Coverage",
    "JUnit.MappingHandleServiceImplTest.executor": "Coverage",
    "JUnit.MccCodeServiceTest.executor": "Run",
    "JUnit.MessageHandleServiceImplTest.executor": "Coverage",
    "JUnit.NonFinancialTranSendMessageTest.executor": "Coverage",
    "JUnit.NotificationEventHandlerImplTest.executor": "Coverage",
    "JUnit.NotificationHandleServiceImplTest.executor": "Coverage",
    "JUnit.NotificationSendMessageTest.executor": "Coverage",
    "JUnit.OrganizationInfoServiceTest.executor": "Run",
    "JUnit.ParamAccountantDictServiceTest.executor": "Run",
    "JUnit.ParmAcctPmtAllocPersonalServiceTest.executor": "Coverage",
    "JUnit.ParmAcctPmtAllocPersonalServiceTest.testAdd_Success.executor": "Run",
    "JUnit.ParmAnnualFeeRuleServiceTest.executor": "Coverage",
    "JUnit.ParmAnnualFeeRuleServiceTest.testFindPage_Success.executor": "Run",
    "JUnit.ParmCardFeeDefiniTionServiceTest.executor": "Run",
    "JUnit.ParmCardFeeGroupServiceTest.executor": "Run",
    "JUnit.ParmTransFeeServiceTest.executor": "Run",
    "JUnit.PartitionKeyInitServiceTest.executor": "Coverage",
    "JUnit.PaymentAllocatedServiceTest.executor": "Run",
    "JUnit.SchedulerFileProcessorTest.executor": "Coverage",
    "JUnit.SchedulerFileProcessorTest.shouldReturnFalse_whenFileCopyFails_thenNotCallScheduler.executor": "Run",
    "JUnit.SchedulerFileProcessorTest.shouldReturnFalse_whenTaskIsRunning_thenWaitForCompletion.executor": "Run",
    "JUnit.SharedInfoFindServiceTest.executor": "Coverage",
    "JUnit.SimpleFileProcessorTest.executor": "Coverage",
    "JUnit.SinagHolidayServiceTest.executor": "Run",
    "JUnit.TransactionCodeServiceTest.executor": "Run",
    "JUnit.WechatPushHandleServiceImplTest.executor": "Coverage",
    "JUnit.WechatSendMessageTest.executor": "Coverage",
    "JUnit.service in anytxn-parameter-sdk (1).executor": "Coverage",
    "JUnit.service in anytxn-parameter-sdk.executor": "Coverage",
    "Maven.anytxn-Product-AI [clean].executor": "Run",
    "Maven.anytxn-Product-AI [compile].executor": "Run",
    "Maven.anytxn-Product-AI [install].executor": "Run",
    "Maven.anytxn-business-core [clean].executor": "Run",
    "Maven.anytxn-business-core [install].executor": "Run",
    "Maven.anytxn-common [clean].executor": "Run",
    "Maven.anytxn-notification [clean].executor": "Run",
    "Maven.anytxn-notification-sdk [compile].executor": "Run",
    "Maven.anytxn-notification-sdk [test].executor": "Run",
    "Maven.anytxn-parameter [clean].executor": "Run",
    "Maven.anytxn-parameter [compile].executor": "Run",
    "Maven.anytxn-parameter [install].executor": "Run",
    "Maven.anytxn-parameter-base [clean].executor": "Run",
    "Maven.anytxn-parameter-batch [clean].executor": "Run",
    "Maven.anytxn-parameter-client [clean].executor": "Run",
    "Maven.anytxn-parameter-sdk [clean].executor": "Run",
    "Maven.anytxn-parent [clean].executor": "Run",
    "Maven.anytxn-parent [compile].executor": "Run",
    "Maven.anytxn-parent [install].executor": "Run",
    "Maven.anytxn-third-party-service [clean].executor": "Run",
    "Maven.anytxn-third-party-service [compile].executor": "Run",
    "Maven.anytxn-third-party-service [install].executor": "Run",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager": "true",
    "Spring Boot.FileManagerServerApplication.executor": "Run",
    "Spring Boot.NotificationServerApplication.executor": "Run",
    "git-widget-placeholder": "feature__project-ai-unittest-20250610",
    "ignore.virus.scanning.warn.message": "true",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "D:/Riveretech/anytxn-Product-AI/.claude",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "Modules",
    "project.structure.proportion": "0.15429688",
    "project.structure.side.proportion": "0.30982658",
    "run.code.analysis.last.selected.profile": "pProject Default",
    "run.configurations.included.in.services": "true",
    "settings.editor.selected.configurable": "configurable.group.build",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\Riveretech\anytxn-Product-AI\.claude" />
      <recent name="E:\Riveretech\anytxn-Product-AI\anytxn-third-party-service\anytxn-notification\anytxn-notification-server\src\main\resources" />
      <recent name="E:\Riveretech\anytxn-Product-AI\anytxn-third-party-service\anytxn-file-manager\anytxn-file-manager-server\src\main\resources" />
      <recent name="E:\Riveretech\anytxn-Product-AI\anytxn-business-core\anytxn-business-core-sdk\src\test\java\com\anytech\anytxn\business\installment\service" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\Riveretech\anytxn-Product-AI" />
      <recent name="E:\Riveretech\anytxn-Product-AI\anytxn-third-party-service" />
    </key>
    <key name="MoveClassesOrPackagesDialog.RECENTS_KEY">
      <recent name="com.anytech.anytxn.parameter.card.service" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.anytech.anytxn.parameter.common.service.card" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="JUnit.ParamAccountantDictServiceTest">
    <configuration name="AcctLimitCtrlServiceTest" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="anytxn-parameter-sdk" />
      <shortenClasspath name="ARGS_FILE" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.anytech.anytxn.parameter.account.service.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.anytech.anytxn.parameter.account.service" />
      <option name="MAIN_CLASS_NAME" value="com.anytech.anytxn.parameter.account.service.AcctLimitCtrlServiceTest" />
      <option name="METHOD_NAME" value="" />
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="DelayedCloseDaysServiceTest" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="anytxn-parameter-sdk" />
      <shortenClasspath name="ARGS_FILE" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.anytech.anytxn.parameter.account.service.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.anytech.anytxn.parameter.account.service" />
      <option name="MAIN_CLASS_NAME" value="com.anytech.anytxn.parameter.account.service.DelayedCloseDaysServiceTest" />
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ParamAccountantDictServiceTest" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="anytxn-parameter-sdk" />
      <shortenClasspath name="ARGS_FILE" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.anytech.anytxn.parameter.accounting.service.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.anytech.anytxn.parameter.accounting.service" />
      <option name="MAIN_CLASS_NAME" value="com.anytech.anytxn.parameter.accounting.service.ParamAccountantDictServiceTest" />
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="JUnit" factoryName="JUnit">
      <shortenClasspath name="ARGS_FILE" />
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="service in anytxn-parameter-sdk (1)" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="anytxn-parameter-sdk" />
      <shortenClasspath name="ARGS_FILE" />
      <option name="TEST_OBJECT" value="directory" />
      <dir value="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/account/service" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="service in anytxn-parameter-sdk" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="anytxn-parameter-sdk" />
      <shortenClasspath name="ARGS_FILE" />
      <option name="TEST_OBJECT" value="directory" />
      <dir value="$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/test/java/com/anytech/anytxn/parameter/authorization/service" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="AnyTxnFileApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="anytxn-file-manager-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.anytech.anytxn.file.server.AnyTxnFileApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="AnytxnNotificationServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="anytxn-notification-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.anytech.anytxn.notification.server.AnytxnNotificationServerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="AnytxnParameterBatchApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="anytxn-parameter-batch" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.anytech.anytxn.parameter.batch.AnytxnParameterBatchApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="FileManagerServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="anytxn-file-manager-server" />
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.anytech.anytxn.file.server.FileManagerServerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="HsmServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="anytxn-hsm-server" />
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.anytech.anytxn.hsm.server.HsmServerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="NotificationServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="anytxn-notification-server" />
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.anytech.anytxn.notification.server.NotificationServerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="JUnit.ParamAccountantDictServiceTest" />
      <item itemvalue="JUnit.service in anytxn-parameter-sdk" />
      <item itemvalue="JUnit.AcctLimitCtrlServiceTest" />
      <item itemvalue="JUnit.DelayedCloseDaysServiceTest" />
      <item itemvalue="JUnit.service in anytxn-parameter-sdk (1)" />
      <item itemvalue="Spring Boot.AnyTxnFileApplication" />
      <item itemvalue="Spring Boot.AnytxnNotificationServerApplication" />
      <item itemvalue="Spring Boot.AnytxnParameterBatchApplication" />
      <item itemvalue="Spring Boot.FileManagerServerApplication" />
      <item itemvalue="Spring Boot.HsmServerApplication" />
      <item itemvalue="Spring Boot.NotificationServerApplication" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="JUnit.ParamAccountantDictServiceTest" />
        <item itemvalue="JUnit.AcctLimitCtrlServiceTest" />
        <item itemvalue="JUnit.service in anytxn-parameter-sdk" />
        <item itemvalue="JUnit.service in anytxn-parameter-sdk (1)" />
        <item itemvalue="JUnit.DelayedCloseDaysServiceTest" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26927.53" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.26927.53" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="1c24e1f2-ced0-42ea-8c34-6fb3e336fa64" name="Changes" comment="" />
      <created>1749606799043</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749606799043</updated>
      <workItem from="1749606800150" duration="11336000" />
      <workItem from="1749707990155" duration="342000" />
      <workItem from="1749708632803" duration="10327000" />
      <workItem from="1749778965877" duration="1607000" />
      <workItem from="1749780811392" duration="12852000" />
      <workItem from="1749864367308" duration="18346000" />
      <workItem from="1749890895500" duration="8012000" />
      <workItem from="1750034854931" duration="1864000" />
      <workItem from="1750037205344" duration="3265000" />
      <workItem from="1750040559282" duration="237000" />
      <workItem from="1750040808101" duration="12044000" />
      <workItem from="1750057776058" duration="9495000" />
      <workItem from="1750067622535" duration="4350000" />
      <workItem from="1750074883845" duration="3857000" />
      <workItem from="1750121163612" duration="25228000" />
      <workItem from="1750210396963" duration="8868000" />
      <workItem from="1750231571548" duration="1258000" />
      <workItem from="1750232956846" duration="1776000" />
      <workItem from="1750234751271" duration="899000" />
      <workItem from="1750235877825" duration="3727000" />
      <workItem from="1750332963418" duration="3406000" />
      <workItem from="1750381389559" duration="5788000" />
      <workItem from="1750399511818" duration="10409000" />
      <workItem from="1750412871122" duration="1614000" />
      <workItem from="1750640908143" duration="1017000" />
      <workItem from="1750641947542" duration="1770000" />
      <workItem from="1750643759284" duration="13000" />
      <workItem from="1750743399024" duration="15021000" />
      <workItem from="1750814479796" duration="169000" />
      <workItem from="1750815155613" duration="11455000" />
      <workItem from="1750901173684" duration="2870000" />
      <workItem from="1750904245018" duration="2809000" />
      <workItem from="1750920999916" duration="8496000" />
      <workItem from="1750986440351" duration="23315000" />
      <workItem from="1751020723514" duration="697000" />
      <workItem from="1751247427474" duration="6235000" />
      <workItem from="1751254253422" duration="5118000" />
      <workItem from="1751273460562" duration="1518000" />
      <workItem from="1751338459022" duration="1093000" />
      <workItem from="1751340030112" duration="16000" />
      <workItem from="1752463159634" duration="2019000" />
      <workItem from="1753175112218" duration="1244000" />
      <workItem from="1753234922878" duration="34000" />
      <workItem from="1753254203848" duration="311000" />
      <workItem from="1753254538096" duration="10309000" />
      <workItem from="1753319309654" duration="9173000" />
      <workItem from="1753328947368" duration="15457000" />
    </task>
    <task id="LOCAL-00001" summary="update: 完成了anytxn-third-party-service的注释完善工作">
      <option name="closed" value="true" />
      <created>1749779525182</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1749779525182</updated>
    </task>
    <task id="LOCAL-00002" summary="update: 部分完成了anytxn-business-core-base的注释完善工作">
      <option name="closed" value="true" />
      <created>1750037967299</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1750037967299</updated>
    </task>
    <task id="LOCAL-00003" summary="update: 完成了anytxn-third-party-service的单元测试工作">
      <option name="closed" value="true" />
      <created>1750039070589</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1750039070589</updated>
    </task>
    <task id="LOCAL-00004" summary="update: 修复了anytxn-parent的pom文件">
      <option name="closed" value="true" />
      <created>1750069184679</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1750069184679</updated>
    </task>
    <task id="LOCAL-00005" summary="update: 部分完成了anytxn-third-party-service的日志规范化工作">
      <option name="closed" value="true" />
      <created>1750210676040</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1750210676040</updated>
    </task>
    <task id="LOCAL-00006" summary="update: 部分完成了anytxn-third-party-service的日志规范化工作">
      <option name="closed" value="true" />
      <created>1750210961931</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1750210961931</updated>
    </task>
    <task id="LOCAL-00007" summary="update: 完成了anytxn-third-party-service的日志规范化工作">
      <option name="closed" value="true" />
      <created>1750234075578</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1750234075578</updated>
    </task>
    <task id="LOCAL-00008" summary="update: 修改了anytxn-business-core的PenaltyParamRuleConstant的构造函数类名命名问题">
      <option name="closed" value="true" />
      <created>1750234537040</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1750234537040</updated>
    </task>
    <task id="LOCAL-00009" summary="update: 生成了anytxn-business-core的单元测试工作">
      <option name="closed" value="true" />
      <created>1750643681422</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1750643681422</updated>
    </task>
    <task id="LOCAL-00010" summary="update: 部分解决了anytxn-business-core的单元测试覆盖率低的问题">
      <option name="closed" value="true" />
      <created>1750764429285</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1750764429285</updated>
    </task>
    <task id="LOCAL-00011" summary="update: 部分解决了anytxn-parameter的包名命名错误的问题">
      <option name="closed" value="true" />
      <created>1751339334021</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1751339334021</updated>
    </task>
    <task id="LOCAL-00012" summary="update: 生成部分了anytxn-parameter的单元测试类">
      <option name="closed" value="true" />
      <created>1751339491728</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1751339491728</updated>
    </task>
    <option name="localTasksCounter" value="13" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="update: 完成了anytxn-third-party-service的注释完善工作" />
    <MESSAGE value="update: 部分完成了anytxn-business-core-base的注释完善工作" />
    <MESSAGE value="update: 完成了anytxn-third-party-service的单元测试工作" />
    <MESSAGE value="update: 修复了anytxn-parent的pom文件" />
    <MESSAGE value="update: 部分完成了anytxn-third-party-service的日志规范化工作" />
    <MESSAGE value="update: 完成了anytxn-third-party-service的日志规范化工作" />
    <MESSAGE value="update: 修改了anytxn-business-core的PenaltyParamRuleConstant的构造函数类名命名问题" />
    <MESSAGE value="update: 生成了anytxn-business-core的单元测试工作" />
    <MESSAGE value="update: 部分解决了anytxn-business-core的单元测试覆盖率低的问题" />
    <MESSAGE value="update: 部分解决了anytxn-parameter的包名命名错误的问题" />
    <MESSAGE value="update: 生成部分了anytxn-parameter的单元测试类" />
    <option name="LAST_COMMIT_MESSAGE" value="update: 生成部分了anytxn-parameter的单元测试类" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/anytxn_Product_AI$ParmCardFeeDefiniTionServiceTest.ic" NAME="ParmCardFeeDefiniTionServiceTest Coverage Results" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true">
      <FILTER>com.anytech.anytxn.parameter.card.service.*</FILTER>
    </SUITE>
    <SUITE FILE_PATH="coverage/anytxn_Product_AI$CusAccountCommonServiceTest.ic" NAME="CusAccountCommonServiceTest Coverage Results" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true">
      <FILTER>com.anytech.anytxn.business.monetary.service.*</FILTER>
    </SUITE>
    <SUITE FILE_PATH="coverage/anytxn_Product_AI$SchedulerFileProcessorTest.ic" NAME="SchedulerFileProcessorTest Coverage Results" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true">
      <FILTER>com.anytech.anytxn.file.service.processor.*</FILTER>
    </SUITE>
    <SUITE FILE_PATH="coverage/anytxn_Product_AI$CardCustSpecialInfoServiceTest.ic" NAME="CardCustSpecialInfoServiceTest Coverage Results" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true">
      <FILTER>com.anytech.anytxn.business.card.service.*</FILTER>
    </SUITE>
    <SUITE FILE_PATH="coverage/anytxn_Product_AI$MessageHandleServiceImplTest.ic" NAME="MessageHandleServiceImplTest Coverage Results" MODIFIED="1750140522768" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true">
      <FILTER>com.anytech.anytxn.notification.service.*</FILTER>
    </SUITE>
    <SUITE FILE_PATH="coverage/anytxn_Product_AI$CardAcctCustReleationServiceTest.ic" NAME="CardAcctCustReleationServiceTest Coverage Results" MODIFIED="1750642864488" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true">
      <FILTER>com.anytech.anytxn.business.card.service.*</FILTER>
    </SUITE>
    <SUITE FILE_PATH="coverage/anytxn_Product_AI$CardManageFeeRecordServiceTest.ic" NAME="CardManageFeeRecordServiceTest Coverage Results" MODIFIED="1750642645265" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true">
      <FILTER>com.anytech.anytxn.business.card.service.*</FILTER>
    </SUITE>
    <SUITE FILE_PATH="coverage/anytxn_Product_AI$PartitionKeyInitServiceTest.ic" NAME="PartitionKeyInitServiceTest Coverage Results" MODIFIED="1750643358947" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true">
      <FILTER>com.anytech.anytxn.business.common.service.*</FILTER>
    </SUITE>
    <SUITE FILE_PATH="coverage/anytxn_Product_AI$AcctLimitCtrlServiceTest.ic" NAME="AcctLimitCtrlServiceTest Coverage Results" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true">
      <FILTER>com.anytech.anytxn.parameter.account.service.*</FILTER>
    </SUITE>
    <SUITE FILE_PATH="coverage/anytxn_Product_AI$NotificationHandleServiceImplTest.ic" NAME="NotificationHandleServiceImplTest Coverage Results" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true">
      <FILTER>com.anytech.anytxn.notification.service.*</FILTER>
    </SUITE>
    <SUITE FILE_PATH="coverage/anytxn_Product_AI$AppPushSendMessageTest.ic" NAME="AppPushSendMessageTest Coverage Results" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true">
      <FILTER>com.anytech.anytxn.notification.service.strategy.*</FILTER>
    </SUITE>
    <SUITE FILE_PATH="coverage/anytxn_Product_AI$ParmAnnualFeeRuleServiceTest.ic" NAME="ParmAnnualFeeRuleServiceTest Coverage Results" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true">
      <FILTER>com.anytech.anytxn.parameter.common.service.card.*</FILTER>
    </SUITE>
    <SUITE FILE_PATH="coverage/anytxn_Product_AI$MappingHandleServiceImplTest.ic" NAME="MappingHandleServiceImplTest Coverage Results" MODIFIED="1750140434921" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true">
      <FILTER>com.anytech.anytxn.notification.service.*</FILTER>
    </SUITE>
    <SUITE FILE_PATH="coverage/anytxn_Product_AI$ExceptionRecordServiceTest.ic" NAME="ExceptionRecordServiceTest Coverage Results" MODIFIED="1750414029040" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true">
      <FILTER>com.anytech.anytxn.business.authorization.service.*</FILTER>
    </SUITE>
    <SUITE FILE_PATH="coverage/anytxn_Product_AI$WechatSendMessageTest.ic" NAME="WechatSendMessageTest Coverage Results" MODIFIED="1750149566119" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true">
      <FILTER>com.anytech.anytxn.notification.service.strategy.*</FILTER>
    </SUITE>
    <SUITE FILE_PATH="coverage/anytxn_Product_AI$NotificationSendMessageTest.ic" NAME="NotificationSendMessageTest Coverage Results" MODIFIED="1750140310081" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true">
      <FILTER>com.anytech.anytxn.notification.service.strategy.*</FILTER>
    </SUITE>
    <SUITE FILE_PATH="coverage/anytxn_Product_AI$FileManagerScanProcessServiceTest.ic" NAME="FileManagerScanProcessServiceTest Coverage Results" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true">
      <FILTER>com.anytech.anytxn.file.service.*</FILTER>
    </SUITE>
    <SUITE FILE_PATH="coverage/anytxn_Product_AI$AccountProductInfoMapperTest.ic" NAME="AccountProductInfoMapperTest Coverage Results" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true">
      <FILTER>com.anytech.anytxn.parameter.cache.*</FILTER>
    </SUITE>
    <SUITE FILE_PATH="coverage/anytxn_Product_AI$FileCopyServiceTest.ic" NAME="FileCopyServiceTest Coverage Results" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true">
      <FILTER>com.anytech.anytxn.file.service.*</FILTER>
    </SUITE>
    <SUITE FILE_PATH="coverage/anytxn_Product_AI$ParmAcctPmtAllocPersonalServiceTest.ic" NAME="ParmAcctPmtAllocPersonalServiceTest Coverage Results" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true">
      <FILTER>com.anytech.anytxn.parameter.account.service.*</FILTER>
    </SUITE>
    <SUITE FILE_PATH="coverage/anytxn_Product_AI$AccountantGlvatbalSelfMapperTest.ic" NAME="AccountantGlvatbalSelfMapperTest Coverage Results" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true">
      <FILTER>com.anytech.anytxn.business.accounting.*</FILTER>
    </SUITE>
    <SUITE FILE_PATH="coverage/anytxn_Product_AI$CreateSecretKeyControllerTest.ic" NAME="CreateSecretKeyControllerTest Coverage Results" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true">
      <FILTER>com.anytech.anytxn.hsm.controller.*</FILTER>
    </SUITE>
    <SUITE FILE_PATH="coverage/anytxn_Product_AI$SharedInfoFindServiceTest.ic" NAME="SharedInfoFindServiceTest Coverage Results" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true">
      <FILTER>com.anytech.anytxn.business.common.service.*</FILTER>
    </SUITE>
    <SUITE FILE_PATH="coverage/anytxn_Product_AI$AccountAbsStautsServiceTest.ic" NAME="AccountAbsStautsServiceTest Coverage Results" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true">
      <FILTER>com.anytech.anytxn.business.accounting.service.*</FILTER>
    </SUITE>
    <SUITE FILE_PATH="coverage/anytxn_Product_AI$AccountCommonServiceTest.ic" NAME="AccountCommonServiceTest Coverage Results" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true">
      <FILTER>com.anytech.anytxn.business.account.service.*</FILTER>
    </SUITE>
    <SUITE FILE_PATH="coverage/anytxn_Product_AI$CommonAccountServiceTest.ic" NAME="CommonAccountServiceTest Coverage Results" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true">
      <FILTER>com.anytech.anytxn.business.account.service.*</FILTER>
    </SUITE>
    <SUITE FILE_PATH="coverage/anytxn_Product_AI$CardMdesNotificationServiceTest.ic" NAME="CardMdesNotificationServiceTest Coverage Results" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true">
      <FILTER>com.anytech.anytxn.business.card.service.*</FILTER>
    </SUITE>
    <SUITE FILE_PATH="coverage/anytxn_Product_AI$FileScanMainOperatorTest.ic" NAME="FileScanMainOperatorTest Coverage Results" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true">
      <FILTER>com.anytech.anytxn.file.service.*</FILTER>
    </SUITE>
    <SUITE FILE_PATH="coverage/anytxn_Product_AI$InstallmentLimitUnitCrossServiceTest.ic" NAME="InstallmentLimitUnitCrossServiceTest Coverage Results" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true">
      <FILTER>com.anytech.anytxn.business.installment.service.*</FILTER>
    </SUITE>
    <SUITE FILE_PATH="coverage/anytxn_Product_AI$service_in_anytxn_parameter_sdk.ic" NAME="service in anytxn-parameter-sdk Coverage Results" MODIFIED="1753347158254" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true" />
    <SUITE FILE_PATH="coverage/anytxn_Product_AI$JdbcServiceProxyTest.ic" NAME="JdbcServiceProxyTest Coverage Results" MODIFIED="1750764261341" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true">
      <FILTER>com.anytech.anytxn.business.monetary.service.*</FILTER>
    </SUITE>
    <SUITE FILE_PATH="coverage/anytxn_Product_AI$MaintenanceLogBisServiceTest.ic" NAME="MaintenanceLogBisServiceTest Coverage Results" MODIFIED="1750751946829" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true">
      <FILTER>com.anytech.anytxn.business.common.service.*</FILTER>
    </SUITE>
    <SUITE FILE_PATH="coverage/anytxn_Product_AI$CorporateTopDownReferenceServiceTest.ic" NAME="CorporateTopDownReferenceServiceTest Coverage Results" MODIFIED="1750643098092" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true">
      <FILTER>com.anytech.anytxn.business.customer.service.*</FILTER>
    </SUITE>
    <SUITE FILE_PATH="coverage/anytxn_Product_AI$WechatPushHandleServiceImplTest.ic" NAME="WechatPushHandleServiceImplTest Coverage Results" MODIFIED="1750140708316" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true">
      <FILTER>com.anytech.anytxn.notification.service.*</FILTER>
    </SUITE>
    <SUITE FILE_PATH="coverage/anytxn_Product_AI$service_in_anytxn_parameter_sdk__1_.ic" NAME="service in anytxn-parameter-sdk (1) Coverage Results" MODIFIED="1753347012817" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true" />
    <SUITE FILE_PATH="coverage/anytxn_Product_AI$IEncryptionControllerTest.ic" NAME="IEncryptionControllerTest Coverage Results" MODIFIED="1750144227309" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true">
      <FILTER>com.anytech.anytxn.hsm.controller.*</FILTER>
    </SUITE>
    <SUITE FILE_PATH="coverage/anytxn_Product_AI$FinancialTranSendMessageTest.ic" NAME="FinancialTranSendMessageTest Coverage Results" MODIFIED="1750140262061" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true">
      <FILTER>com.anytech.anytxn.notification.service.strategy.*</FILTER>
    </SUITE>
    <SUITE FILE_PATH="coverage/anytxn_Product_AI$AQRCTest.ic" NAME="AQRCTest Coverage Results" MODIFIED="1750140083052" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true" />
    <SUITE FILE_PATH="coverage/anytxn_Product_AI$CardBinDefinitionServiceImplTest.ic" NAME="CardBinDefinitionServiceImplTest Coverage Results" MODIFIED="1753342571106" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true">
      <FILTER>com.anytech.anytxn.parameter.common.service.card.*</FILTER>
    </SUITE>
    <SUITE FILE_PATH="coverage/anytxn_Product_AI$EmailSendMessageTest.ic" NAME="EmailSendMessageTest Coverage Results" MODIFIED="1750140229733" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true">
      <FILTER>com.anytech.anytxn.notification.service.strategy.*</FILTER>
    </SUITE>
    <SUITE FILE_PATH="coverage/anytxn_Product_AI$EncryptionApiTest.ic" NAME="EncryptionApiTest Coverage Results" MODIFIED="1750143143155" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true">
      <FILTER>com.anytech.anytxn.hsm.service.*</FILTER>
    </SUITE>
    <SUITE FILE_PATH="coverage/anytxn_Product_AI$CardBinServiceTest.ic" NAME="CardBinServiceTest Coverage Results" MODIFIED="1753342858170" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true">
      <FILTER>com.anytech.anytxn.parameter.card.service.*</FILTER>
    </SUITE>
    <SUITE FILE_PATH="coverage/anytxn_Product_AI$SimpleFileProcessorTest.ic" NAME="SimpleFileProcessorTest Coverage Results" MODIFIED="1750138832432" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true">
      <FILTER>com.anytech.anytxn.file.service.processor.*</FILTER>
    </SUITE>
    <SUITE FILE_PATH="coverage/anytxn_Product_AI$NonFinancialTranSendMessageTest.ic" NAME="NonFinancialTranSendMessageTest Coverage Results" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true">
      <FILTER>com.anytech.anytxn.notification.service.strategy.*</FILTER>
    </SUITE>
    <SUITE FILE_PATH="coverage/anytxn_Product_AI$CustAccountWriterServiceTest.ic" NAME="CustAccountWriterServiceTest Coverage Results" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true">
      <FILTER>com.anytech.anytxn.business.monetary.service.*</FILTER>
    </SUITE>
    <SUITE FILE_PATH="coverage/anytxn_Product_AI$EncryptionManagerTest.ic" NAME="EncryptionManagerTest Coverage Results" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true">
      <FILTER>com.anytech.anytxn.hsm.service.*</FILTER>
    </SUITE>
    <SUITE FILE_PATH="coverage/anytxn_Product_AI$CardCustomServiceTest.ic" NAME="CardCustomServiceTest Coverage Results" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true">
      <FILTER>com.anytech.anytxn.parameter.common.service.card.*</FILTER>
    </SUITE>
    <SUITE FILE_PATH="coverage/anytxn_Product_AI$NotificationEventHandlerImplTest.ic" NAME="NotificationEventHandlerImplTest Coverage Results" MODIFIED="1750140545887" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true">
      <FILTER>com.anytech.anytxn.notification.service.*</FILTER>
    </SUITE>
  </component>
</project>